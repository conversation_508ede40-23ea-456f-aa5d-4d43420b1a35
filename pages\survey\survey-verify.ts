import { Page, expect } from "@playwright/test";

export class SurveyVerify {
  constructor(private page: Page) { }

  async verifyCreateSurveySuccess() {
    await expect(this.page.getByText("Khảo sát của bạn đã được gửi đến những thành viên cần trả lời.")).toBeVisible();
  }

  async verifySurveyDeleted() {
    // Tuỳ UI, có thể check theo message "Không có khảo sát"
    // Không có message nào báo thành công -> khá khó để bắt chính xác
    await expect(this.page.getByText('Không có khảo sát')).toBeVisible();
  }

  // New verify methods for different survey types
  async verifyDailySurvey() {
    await expect(this.page.getByText('Hàng ngày').first()).toBeVisible();
  }

  async verifyWeeklySurvey() {
    await expect(this.page.getByText('Hàng tuần').first()).toBeVisible();
  }

  async verifyMonthlySurvey() {
    await expect(this.page.getByText('Hàng tháng').first()).toBeVisible();
  }

  async verifySurveyTemplate(templateName: string) {
    await expect(this.page.getByText(templateName).nth(1)).toBeVisible();
  }

  async verifyAddedQuestion(title: string) {
    await expect(this.page.getByText(title)).toBeVisible();
  }

  // Generic method to verify survey creation success
  async verifySurveyCreationSuccess() {
    await expect(this.page.getByRole('button', { name: 'Xem khảo sát' })).toBeVisible();
  }
}
