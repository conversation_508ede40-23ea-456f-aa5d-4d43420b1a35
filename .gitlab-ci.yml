stages:
  - test

variables:
  ENV: prod
  BROWSER: chromium

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - node_modules/

test:e2e:
  stage: test
  image: mcr.microsoft.com/playwright:v1.44.1-jammy
  # tags:
  #   - linux  # hoặc bỏ nếu không dùng runner gắn tag
  before_script:
    - echo "Installing dependencies..."
    - npm ci
    - chmod +x ./node_modules/.bin/playwright  # ✅ cấp quyền thực thi cho playwright
    - npx playwright install --with-deps
  script:
    - echo "🚀 Running Playwright tests on $ENV using $BROWSER..."
    - npm run testloginprod
  artifacts:
    when: always
    paths:
      - playwright-report
    expire_in: 7 days

