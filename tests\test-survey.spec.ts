import { Page, test } from "@playwright/test";
import { createStorageState } from "./setup/common-setup";
import { Context } from "vm";
import { NavigateAction } from "../pages/navigate/navigate-action";
import { SurveyAction } from "../pages/survey/survey-action";
import { SurveyVerify } from "../pages/survey/survey-verify";
import surveyData from "../data/survey-data.json";

let page: Page;
let context: Context;
let nav: NavigateAction;
let surveyAction: SurveyAction;
let surveyVerify: SurveyVerify;
const ENV = process.env.ENV || "prod";

test.describe("Survey Tests", () => {
  test.beforeAll(async ({ browser }) => {
    // Tạo storage chung
    const storagePath = await createStorageState(
      browser,
      ENV,
      "survey_account", // account key trong getAccountData()
      `${ENV}-survey-storage-state.json`
    );

    // Mở context từ storage
    context = await browser.newContext({ storageState: storagePath });
    page = await context.newPage();
    nav = new NavigateAction(page);
  });

  test.afterAll(async () => {
    await context.close();
  });

  test.beforeEach(async () => {
    await nav.openUrl();
    await nav.goToSurvey();
    await nav.closeNotiPopup();
    surveyAction = new SurveyAction(page);
    surveyVerify = new SurveyVerify(page);
  });

  test.afterEach(async () => {
    if (test.info().title.includes("delete")) return;
    await nav.goToCreatedSurvey();
    // await surveyAction.cleanUpSurveys();
    await surveyAction.deleteFirstSurvey();
  });

  test("@createsurvey no deadline", async () => {
    await surveyAction.createSurveyNoDeadline(
      surveyData.case1.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifyCreateSurveySuccess();
    await surveyAction.closeSuccessPopup();
  });

  test.only("@createsurveyWithDeadline with deadline", async () => {
    await surveyAction.createSurveyWithDeadline(
      surveyData.case2.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifyCreateSurveySuccess();
    await surveyAction.closeSuccessPopup();
  });

  test("@createsurveyRecurring recurring survey", async () => {
    await surveyAction.createRecurringSurvey(
      surveyData.case3.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifyCreateSurveySuccess();
    await surveyAction.closeSuccessPopup();
  });

  test("@editSurvey change type", async () => {
    await surveyAction.createRecurringSurvey(
      surveyData.case4.participants,
      surveyData.new_survey.title
    );
    await surveyAction.closeSuccessPopup();
    await surveyAction.editFirstSurveyType(surveyData.case4.newType);
    await surveyVerify.verifyCreateSurveySuccess();
  });

  test("@deleteSurvey delete first survey", async () => {
    await nav.goToCreatedSurvey();
    await surveyAction.deleteFirstSurvey();
    await surveyVerify.verifySurveyDeleted();
  });


  // mới
  test("@createDailyRecurringSurvey daily recurring survey", async () => {
    await surveyAction.createDailyRecurringSurvey(
      surveyData.case5.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifySurveyCreationSuccess();
    await surveyAction.closeSuccessPopup();
    await surveyAction.openEditFirstSurvey();
    await surveyVerify.verifyDailySurvey();
  });

  test("@createWeeklyRecurringSurvey weekly recurring survey", async () => {
    await surveyAction.createWeeklyRecurringSurvey(
      surveyData.case6.participants,
      surveyData.case6.dayOfWeek,
      surveyData.new_survey.title
    );
    await surveyVerify.verifySurveyCreationSuccess();
    await surveyAction.closeSuccessPopup();
    await surveyAction.openEditFirstSurvey();
    await surveyVerify.verifyWeeklySurvey();
  });

  test("@createMonthlyRecurringSurvey monthly recurring survey", async () => {
    await surveyAction.createMonthlyRecurringSurvey(
      surveyData.case7.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifySurveyCreationSuccess();
    await surveyAction.closeSuccessPopup();
    await surveyAction.openEditFirstSurvey();
    await surveyVerify.verifyMonthlySurvey();
  });

  test("@createSurveyFromTemplate survey from template", async () => {
    await surveyAction.createSurveyFromTemplate(
      surveyData.case8.templateName,
      surveyData.case8.participants,
      true // isRecurring
    );
    await surveyVerify.verifySurveyCreationSuccess();
    await surveyAction.viewSurveyDetails();
    await page.waitForTimeout(5000);
    await surveyVerify.verifySurveyTemplate(surveyData.case8.templateName);
  });

  test("@addQuestionToSurvey add question to existing survey", async () => {
    // First create a survey from template
    await surveyAction.createSurveyFromTemplate(
      surveyData.case8.templateName,
      surveyData.case8.participants,
      true
    );
    await surveyVerify.verifySurveyCreationSuccess();
    await surveyAction.viewSurveyDetails();

    // Then add a question to it
    await surveyAction.addQuestionToSurvey(surveyData.case8.new_question);
    await surveyVerify.verifySurveyCreationSuccess();
    await surveyAction.viewSurveyDetails();
    await surveyVerify.verifyAddedQuestion(surveyData.case8.new_question);
  });
});
