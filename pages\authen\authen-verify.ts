import { Page } from "@playwright/test";
import { expect } from "@playwright/test";

export class AuthenVerify {
  constructor(private page: Page) {}

  async verifyLoginSuccessfully(): Promise<void> {
    await expect(
      this.page.getByRole("link", { name: "Bảng tin tổ chức" })
    ).toBeVisible();
  }
  async verifyLogoutSuccessfully(): Promise<void> {
    await expect(this.page.locator("#root")).toContainText("Đăng nhập");
  }
}
