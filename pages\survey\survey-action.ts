import { Page } from "@playwright/test";

export class SurveyAction {
  constructor(private page: Page) { }

  // Main survey creation methods
  async createSurveyNoDeadline(participants: string[], title: string) {
    await this.clickCreateSurveyButton();
    await this.selectTextQuestionType();
    await this.addParticipants(participants);
    await this.inputSurveyName(title);
    await this.sendSurvey();
  }

  async createSurveyWithDeadline(
    participants: string[],
    title: string
  ) {
    // 2 ngày sau hiện tại
    const deadlineDate = new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    await this.clickCreateSurveyButton();
    await this.setDeadline(deadlineDate);
    await this.selectTextQuestionType();
    await this.addParticipants(participants);
    await this.inputSurveyName(title);
    await this.page.waitForTimeout(3000);
    await this.sendSurvey();
    await this.page.waitForTimeout(3000);
  }

  // Basic action methods
  private async clickCreateSurveyButton() {
    await this.page.getByRole("button", { name: "<PERSON><PERSON><PERSON> khảo sát" }).click();
  }

  private async selectTextQuestionType() {
    await this.page.getByRole("button", { name: "Đoạn văn bản" }).click();
  }

  private async setDeadline(deadlineDate: string) {
    await this.page.locator("#DeadlineWrapper div").nth(2).click();
    await this.page.getByTitle(deadlineDate).locator("div").click();
    await this.page.getByRole("button", { name: "Hoàn thành" }).click();
  }

  private async addParticipants(participants: string[]) {
    await this.page.getByRole("button", { name: "Thêm người" }).click();
    for (const participant of participants) {
      await this.page.getByText(participant).click();
    }
    await this.page.getByRole("button", { name: "Xong" }).click();
  }

  // Alternative method for adding participants when specific selector is needed
  private async addParticipantsBySelector(participants: string[]) {
    await this.page.getByRole("button", { name: "Thêm người" }).click();
    // Use more specific selector for template case
    await this.page.locator('div > div:nth-child(9) > div').click();
    await this.page.getByRole("button", { name: "Xong" }).click();
  }

  private async sendSurvey() {
    await this.page.getByRole("button", { name: "Gửi khảo sát" }).click();
  }

  private async inputSurveyName(title: string) {
    await this.page.waitForTimeout(1000);
    const titleInput = this.page.locator(".header-Survey__editor div[role='textbox']").first();
    await titleInput.fill("");
    await titleInput.fill(title);
  }

  async closeSuccessPopup() {
    await this.clickCloseButton();
    await this.page.waitForTimeout(2000);
  }

  async createRecurringSurvey(participants: string[], title: string) {
    await this.clickCreateSurveyButton();
    await this.selectRecurringType();
    await this.editRemindToNoRemind();
    await this.selectTextQuestionType();
    await this.addParticipants(participants);
    await this.inputSurveyName(title);
    await this.sendSurvey();
  }

  // Recurring survey specific methods
  private async selectRecurringType() {
    await this.page.getByRole("radio", { name: "Định kỳ" }).check();
  }

  private async clickCloseButton() {
    await this.page.getByRole("button", { name: "Đóng" }).click();
  }

  async editFirstSurveyType(newType: string) {
    await this.openFirstSurveyMenu();
    await this.clickEditOption();
    await this.changeSurveyType(newType);
    await this.saveEditedSurvey();
  }

  async openEditFirstSurvey() {
    await this.openFirstSurveyMenu();
    await this.clickEditOption();
  }

  async editRemindToNoRemind() {
    await this.page.waitForTimeout(1000);
    await this.openReminderDropdown();
    await this.selectNoReminder();
  }

  async deleteFirstSurvey() {
    await this.openFirstSurveyMenu();
    await this.clickDeleteOption();
    await this.confirmDeletion();
  }

  // Survey editing helper methods
  private async openFirstSurveyMenu() {
    await this.page.waitForTimeout(1000);
    await this.page.locator('//button[@aria-haspopup="true"]').first().click();
  }

  private async clickEditOption() {
    await this.page.getByText("Chỉnh sửa", { exact: true }).click();
  }

  private async changeSurveyType(newType: string) {
    await this.page.getByRole("button", { name: "Hàng ngày" }).click();
    await this.page.getByText(newType).click();
  }

  private async saveEditedSurvey() {
    await this.page.getByRole("button", { name: "Sửa khảo sát" }).click();
  }

  private async openReminderDropdown() {
    await this.page.getByRole('button', { name: 'Nhắc trả lời trước 30 phút' }).click();
  }

  private async selectNoReminder() {
    await this.page.getByText('Không nhắc trước').click();
  }

  private async clickDeleteOption() {
    await this.page
      .getByRole("menuitem", { name: "Xóa" })
      .locator("div")
      .first()
      .click();
  }

  private async confirmDeletion() {
    await this.page.getByRole("button", { name: "Xác nhận" }).click();
  }

  async cleanUpSurveys() {
    // Implementation for cleaning up surveys if needed
  }

  // New methods for daily recurring survey
  async createDailyRecurringSurvey(participants: string[], title?: string) {
    await this.clickCreateSurveyButton();
    await this.selectRecurringType();
    await this.selectDailyFrequency();
    await this.editRemindToNoRemind();
    await this.addParticipants(participants);
    await this.selectTextQuestionType();
    if (title) {
      await this.inputSurveyName(title);
    }
    await this.sendSurvey();
  }

  // New methods for weekly recurring survey
  async createWeeklyRecurringSurvey(participants: string[], dayOfWeek: string, title?: string) {
    await this.clickCreateSurveyButton();
    await this.selectRecurringType();
    await this.selectWeeklyFrequency();
    await this.selectDayOfWeek(dayOfWeek);
    await this.editRemindToNoRemind();
    await this.addParticipants(participants);
    await this.selectTextQuestionType();
    if (title) {
      await this.inputSurveyName(title);
    }
    await this.sendSurvey();
  }

  // New methods for monthly recurring survey
  async createMonthlyRecurringSurvey(participants: string[], title?: string) {
    await this.clickCreateSurveyButton();
    await this.selectRecurringType();
    await this.selectMonthlyFrequency();
    await this.editRemindToNoRemind();
    await this.addParticipants(participants);
    await this.selectTextQuestionType();
    if (title) {
      await this.inputSurveyName(title);
    }
    await this.sendSurvey();
  }

  // New method for creating survey from template
  async createSurveyFromTemplate(templateName: string, participants: string[], isRecurring: boolean = false) {
    await this.clickCreateSurveyButton();
    await this.selectSurveyTemplate();
    await this.selectTemplateByName(templateName);
    await this.useSurveyTemplate();
    if (isRecurring) {
      await this.selectRecurringType();
    }
    await this.addParticipantsBySelector(participants);
    await this.editRemindToNoRemind();
    await this.sendSurvey();
  }

  // New method for adding question to existing survey
  async addQuestionToSurvey(title: string) {
    await this.openSurveyEditMode();
    await this.selectTextQuestionType();
    await this.page.keyboard.type(title);
    await this.saveSurveyEdit();
  }

  // Helper methods for new functionality
  private async selectDailyFrequency() {
    await this.page.getByRole('button', { name: 'Hàng ngày' }).click();
    await this.page.getByTestId('popover').getByText('Hàng ngày').click();
  }

  private async selectWeeklyFrequency() {
    await this.page.getByRole('button', { name: 'Hàng ngày' }).click();
    await this.page.getByText('Hàng tuần').click();
  }

  private async selectMonthlyFrequency() {
    await this.page.getByRole('button', { name: 'Hàng ngày' }).click();
    await this.page.getByText('Hàng tháng').click();
  }

  private async selectDayOfWeek(dayOfWeek: string) {
    await this.page.getByRole('button', { name: dayOfWeek }).click();
  }

  private async selectSurveyTemplate() {
    await this.page.getByRole('button', { name: 'Khảo sát mẫu' }).first().click();
  }

  private async selectTemplateByName(templateName: string) {
    await this.page.getByRole('button', { name: templateName }).click();
  }

  private async useSurveyTemplate() {
    await this.page.getByRole('button', { name: 'Sử dụng mẫu' }).click();
  }

  private async openSurveyEditMode() {
    await this.page.getByRole('complementary').getByRole('button').nth(1).click();
  }

  private async saveSurveyEdit() {
    await this.page.getByRole('button', { name: 'Sửa khảo sát' }).click();
  }

  // Method to view survey details
  async viewSurveyDetails() {
    await this.page.getByRole('button', { name: 'Xem khảo sát' }).click();
  }

  // Method to navigate back to survey list
  async goBackToSurveyList() {
    await this.page.locator('#feed').getByRole('link', { name: 'Beta' }).click();
    await this.page.getByRole('link', { name: 'Khảo sát' }).click();
  }
}
