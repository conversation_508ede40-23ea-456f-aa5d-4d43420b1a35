import { Page, test } from "@playwright/test";
import { createStorageState } from "./setup/common-setup";
import { NavigateAction } from "../pages/navigate/navigate-action";
import postdata from "../data/post-data.json";
import { getAccountData } from "../helpers/getAccountData";
import path from "path";
import { Context } from "vm";
import { PostVerify } from "../pages/post/post-verify";
import { PostAction } from "../pages/post/post-action";

let page: Page;
let context: Context;
let nav: NavigateAction;
let postAction: PostAction;
let postVerify: PostVerify;
const ENV = process.env.ENV || "prod";

test.describe("Post Timeline Tests", () => {
  test.beforeAll(async ({ browser }) => {
    // Tạo storage chung
    const storagePath = await createStorageState(
      browser,
      ENV,
      "post_account", // account key trong getAccountData()
      `${ENV}-post-storage-state.json`
    );

    // Mở context từ storage
    context = await browser.newContext({ storageState: storagePath });
    page = await context.newPage();
    nav = new NavigateAction(page);
  });

  test.afterAll(async () => {
    await context.close();
  });

  test.beforeEach(async () => {
    await nav.openUrl();
    await nav.clickToProfileIcon();
    await nav.goToTimeLine();

    postAction = new PostAction(page);
    postVerify = new PostVerify(page);
  });

  test("@posttext post text successfully", async () => {
    const postContent = postdata.content;
    await postAction.clickCreatePostBtn();
    await postAction.inputPostContent(postContent);
    await postAction.clickPostBtn();
    await postVerify.verifyPostContent(postContent);
    await postAction.viewPostDetail();
    await postVerify.verifyPostContent(postContent);
  });

  test("post media successfully", async () => {
    const postContent = postdata.content;
    const media1 = path.resolve(__dirname, "../data/images/image1.jpg");
    const media2 = path.resolve(__dirname, "../data/images/image2.jpg");
    const media3 = path.resolve(__dirname, "../data/images/gif.gif");

    await postAction.clickCreatePostBtn();
    await postAction.inputPostContent(postContent);
    await postAction.uploadMediaToPost([media1, media2, media3]);
    await postAction.clickPostBtn();

    await postVerify.verifyPostContent(postContent);
    await postVerify.verifyPostMediaNumber(3);

    await postAction.viewPostDetail();
    await postVerify.verifyPostContent(postContent);
    await postVerify.verifyPostMediaNumber(3);
  });

  test("@postbackground post background successfully", async () => {
    const postContent = postdata.content_background;
    await postAction.clickCreatePostBtn();
    await postAction.selectPostBackground();
    await postAction.inputPostContent(postContent);
    await postAction.clickPostBtn();

    await postVerify.verifyPostContent(postContent);
    await postVerify.verifyBackground();

    await postAction.viewPostDetail();
    await postVerify.verifyPostContent(postContent);
    await postVerify.verifyBackground();
  });

  test("@posttag post tag user successfully", async () => {
    const postContent = postdata.content;
    const postTagNumber = postdata.post_tag_number;
    await postAction.clickCreatePostBtn();
    await postAction.inputPostContent(postContent);
    await postAction.selectUserToTag(postTagNumber);
    await postAction.clickPostBtn();
    await postVerify.verifyPostContent(postContent);
    await postAction.viewPostDetail();
    await postVerify.verifyPostContent(postContent);
    await postVerify.verifyTagUserList(postTagNumber);
  });

  test("@postmention post mention user successfully", async () => {
    const postContent = postdata.content;
    await nav.acceptNotiPopup();
    await postAction.clickCreatePostBtn();
    await postAction.mentionUserInPost();
    await postAction.clickPostBtn();
    await nav.acceptNotiPopup();
    await postAction.clickToFirstUserMentionInPost();
    await postVerify.verifyMentionPost();
  });

  test("@postaskme post ask me successfully", async () => {
    const postContent = postdata.content;
    const userName = getAccountData().post_account.user_name;
    await postAction.clickCreatePostBtn();
    await postAction.clickToCreatePostAskMe();
    await postAction.inputPostContent(postContent);
    await postAction.clickPostBtn();
    await postVerify.verifyPostContent(postContent);
    await postVerify.verifyPostAskMe(userName);
    await postAction.viewPostDetail();
    await postVerify.verifyPostContent(postContent);
    await postVerify.verifyPostAskMe(userName);
  });

  test("@postpollvote post pollvote successfully", async () => {
    const postContent = postdata.content;
    const pollvoteQuestion = postdata.pollvote_question;
    const voteOptions = postdata.pollvote_options;
    console.log(voteOptions);
    await postAction.clickCreatePostBtn();
    await postAction.clickToCreatePostPollvote();
    await postAction.inputPollvoteInfor(pollvoteQuestion, voteOptions);
    await postAction.clickContinueCreatePollVote();
    await postAction.clickPostBtn();
    await postVerify.verifyPostPollvote(pollvoteQuestion, voteOptions);
    await postAction.viewPostDetail();
    await postVerify.verifyPostPollvote(pollvoteQuestion, voteOptions);
  });

  test.afterEach(async () => {
    nav = new NavigateAction(page);
    await nav.clickToProfileIcon();
    await nav.goToTimeLine();
    await page.waitForTimeout(1000);
    await nav.acceptNotiPopup();
    await postAction.deleteAllPost();
  });
});
